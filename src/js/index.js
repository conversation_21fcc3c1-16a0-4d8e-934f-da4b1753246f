import "../css/index.css";
// import "lenis/dist/lenis.css";

import "./usp";

import focus from "@alpinejs/focus";
import Alpine from "alpinejs";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import Lenis from "lenis";

gsap.registerPlugin(ScrollTrigger);

window.Alpine = Alpine;

Alpine.plugin(focus);
Alpine.start();

/**
 * Accept HMR as per: https://vitejs.dev/guide/api-hmr.html & https://nystudio107.com/docs/vite/
 */
if (import.meta.hot) {
	import.meta.hot.accept(() => {
		console.log("HMR");
	});
}

// const lenis = new Lenis();

// lenis.on("scroll", ScrollTrigger.update);

// gsap.ticker.add((time) => {
// 	lenis.raf(time * 1000);
// });

// gsap.ticker.lagSmoothing(0);
