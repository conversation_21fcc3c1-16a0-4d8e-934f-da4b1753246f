import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const STEP_PX = 450; // pixels per step

const init = (section) => {
	const container = section.querySelector(".container");

	const textEls = [...container.querySelectorAll("[data-text]")].sort(
		(a, b) => +a.dataset.text - +b.dataset.text,
	);
	const imgEls = [...container.querySelectorAll("[data-image]")].sort(
		(a, b) => +a.dataset.image - +b.dataset.image,
	);

	const n = imgEls.length;
	if (n === 0) return;

	// Helpers (depth 1 = top-most)
	const xFor = (depth) => depth * 10; // %
	const scaleFor = (depth) => 1 - depth * 0.1;
	const zFor = (depth) => n - depth + 1;

	// Initial state
	imgEls.forEach((el, i) => {
		const depth = i + 1;
		gsap.set(el, {
			xPercent: xFor(depth),
			scale: scaleFor(depth),
			yPercent: -50,
			zIndex: zFor(depth),
			autoAlpha: 1,
		});
	});
	gsap.set(textEls, { autoAlpha: 0 });
	if (textEls[0]) gsap.set(textEls[0], { autoAlpha: 1 });

	// Max step is n-1 so the last image never exits
	const MAX_STEP = Math.max(0, n - 1);
	let step = 0;

	const setStep = (n) => {
		next = gsap.utils.clamp(0, MAX_STEP, next);
		if (next === step) return;
		step = next;

		// Images: j <= step => out; j > step => shift forward
		imgEls.forEach((el, i) => {
			const j = i + 1; // 1..n
			if (j <= step) {
				gsap.to(el, {
					yPercent: -180,
					autoAlpha: 0,
					duration: 0.5,
					overwrite: "auto",
				});
			} else {
				const depth = j - step; // 1 for new top, then 2,3...
				gsap.to(el, {
					yPercent: -50,
					xPercent: xFor(depth),
					scale: scaleFor(depth),
					zIndex: zFor(depth),
					autoAlpha: 1,
					duration: 0.5,
					overwrite: "auto",
				});
			}
		});

		// Text: show text[step] (last text stays)
		textEls.forEach((t, idx) => {
			gsap.to(t, {
				autoAlpha: idx === step ? 1 : 0,
				duration: 0.25,
				overwrite: "auto",
			});
		});
	};

	// Ensure pin spacing can expand
	if (section.classList.contains("h-screen")) {
		section.classList.remove("h-screen");
		section.classList.add("min-h-screen");
	}

	const totalScroll = MAX_STEP * STEP_PX; // last image stays, so one fewer step
	ScrollTrigger.create({
		trigger: section,
		start: "top top",
		end: `+=${totalScroll}`,
		pin: section,
		pinSpacing: true,
		anticipatePin: 1,
		invalidateOnRefresh: true,
		onUpdate: (self) => {
			const dist = self.progress * totalScroll;
			const next = Math.floor(dist / STEP_PX + 1e-6); // 0..MAX_STEP
			setStep(next);
		},
		onEnter: () => setStep(0),
		onEnterBack: () => setStep(0),
	});

	// Refresh after images load
	const imgs = section.querySelectorAll("img");
	let loaded = 0;
	const total = imgs.length;
	const done = () => ScrollTrigger.refresh();
	if (!total) done();
	for (const img of imgs) {
		if (img.complete) {
			if (++loaded === total) done();
		} else
			img.addEventListener(
				"load",
				() => {
					if (++loaded === total) done();
				},
				{ once: true },
			);
	}
};

document.addEventListener("DOMContentLoaded", () => {
	document.querySelectorAll("[data-section='usp']").forEach(init);
});
